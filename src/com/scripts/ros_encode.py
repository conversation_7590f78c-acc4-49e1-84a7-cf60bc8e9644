#!/usr/bin/env python3
import rospy
from std_msgs.msg import Int8
import sys
sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from com.msg import ControlCommand  # 导入自定义消息

def encode_message(direction, speed):
    """
    将方向和速度编码为二进制消息，格式如下：
    [1 起始位][7 数据位][1 校验位][2 停止位]

    参数：
        direction (int): 方向 (0-31)，表示32个方向。
        speed (int): 速度 (0-3)，表示4个速度等级。

    返回：
        str: 编码后的二进制消息。
    """
    if not (0 <= direction <= 31):
        raise ValueError("方向必须在0到31之间。")
    if not (0 <= speed <= 3):
        raise ValueError("速度必须在0到3之间。")

    # 编码方向（5位）和速度（2位）
    direction_bits = f"{direction:05b}"
    speed_bits = f"{speed:02b}"
    data_bits = direction_bits + speed_bits  # 总共7位数据位

    # 计算奇偶校验位（奇校验）
    parity_bit = '1' if data_bits.count('1') % 2 == 0 else '0'

    # 组成完整的消息
    start_bit = '0'
    stop_bits = '11'
    encoded_message = start_bit + data_bits + parity_bit + stop_bits

    # 重复消息三次
    repeated_message = encoded_message * 3

    return repeated_message

def control_command_callback(data):
    """
    订阅 control_command 话题的回调函数。
    解析接收到的消息并逐个发布编码后的二进制位。
    """
    # 从自定义消息中获取方向和速度
    direction = data.direction
    speed = data.speed

    # 编码消息
    encoded_message = encode_message(direction, speed)
    rospy.loginfo(f"Encoded message: {encoded_message}")
    # 标记已接收到消息
    global received_message
    received_message = True
    # 逐个发布二进制位
    for bit in encoded_message:
        led_cmd = Int8()
        led_cmd.data = int(bit)  # 将 '0' 或 '1' 转换为整数
        pub.publish(led_cmd)
        rospy.loginfo(f"Publishing bit: {bit}")
        rate.sleep()  # 控制发布频率
        
    received_message = False


def publish_led_command():
    # 初始化 ROS 节点
    rospy.init_node('led_publisher', anonymous=True)
    
    # 创建一个发布者，发布到 'robot/ledcmd' 主题
    global pub, rate, received_message
    pub = rospy.Publisher('robot/ledcmd', Int8, queue_size=10)
    
    # 设置发布频率（例如 10 Hz）
    rate = rospy.Rate(20)  # 10 Hz
    
    # 订阅 control_command 话题，使用自定义消息类型 ControlCommand
    rospy.Subscriber('control_command', ControlCommand, control_command_callback)
    
    # 初始化消息接收标志
    received_message = False

    while not rospy.is_shutdown():
        # 如果没有接收到消息，默认发送 1
        if not received_message:
            led_cmd = Int8()
            led_cmd.data = 1
            pub.publish(led_cmd)
            rospy.loginfo("No message received, publishing default: 1")
            received_message = False
        # else:
        #     # 重置消息接收标志
        #     received_message = False
        
        rate.sleep()
        # 初始化消息接收标志


if __name__ == '__main__':
    try:
        publish_led_command()
    except rospy.ROSInterruptException:
        pass