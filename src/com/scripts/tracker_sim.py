#!/usr/bin/env python3
import rospy
from std_msgs.msg import String,Int8
from std_msgs.msg import String as FormattedString  # 用于发布格式化后的消息

# 初始化跟踪ID
tracking_id = 0

def ledcmd_callback(data):
    """
    订阅ledcmd话题的回调函数。
    将接收到的消息转换为具有跟踪ID和消息内容的格式。
    """
    global tracking_id 
    received_message = data.data  # 获取原始消息内容

    # 生成格式化后的消息
    formatted_message = f"ID: {tracking_id}, Message: {received_message}"

    # 更新跟踪ID
    tracking_id = 1

    # 发布格式化后的消息
    pub.publish(formatted_message)
    rospy.loginfo(f"Published formatted message: {formatted_message}")

def publish_formatted_ledcmd():
    """
    初始化ROS节点，订阅ledcmd话题，并发布格式化后的消息。
    """
    # 初始化ROS节点
    rospy.init_node('tracker', anonymous=True)

    # 创建一个发布者，发布到formatted_ledcmd话题
    global pub
    pub = rospy.Publisher('tracker', FormattedString, queue_size=10)

    # 订阅ledcmd话题
    rospy.Subscriber('robot/ledcmd', Int8, ledcmd_callback)

    # 保持节点运行
    rospy.spin()

if __name__ == '__main__':
    try:
        publish_formatted_ledcmd()
    except rospy.ROSInterruptException:
        pass