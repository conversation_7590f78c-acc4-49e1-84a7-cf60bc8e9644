#!/usr/bin/env python

import os
import cv2
import numpy as np
from loguru import logger
from ocsort import OCSort
from detect import ImageDetector
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import sys
import threading
import queue  # 新增队列模块

sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from my_package.msg import ImageWithName, TrackStatus

class ROSImageDetector:
    def __init__(self, output_folder, threshold=(40, 40, 40), area_threshold=10):
        self.detector = ImageDetector(output_folder, threshold, area_threshold)
        self.bridge = CvBridge()
        self.frame_id = 0
        self.lock = threading.Lock()

    def detect(self, frame):
        with self.lock:
            dets = self.detector.detect(frame)
            self.frame_id += 1
        return dets

class ImageProcessor:
    def __init__(self, output_path, tracker_params):
        self.output_path = output_path
        self.tracker_params = tracker_params

        self.tracker = OCSort(**tracker_params)
        self.colours = np.random.rand(64, 3) * 255
        self.id_status = {}
        self.frame_id = 0
        self.lock = threading.Lock()
        
        # 新增视频显示相关属性
        self.display_queue = queue.Queue(maxsize=30)  # 显示队列
        self.display_thread = None
        self.display_running = True

        # 初始化显示窗口
        cv2.namedWindow('Tracking Preview', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('Tracking Preview', 1280, 720)

        # 启动显示线程
        self.start_display_thread()

        # 创建保存目录
        self.frames_folder = f"{output_path}_frames"
        os.makedirs(self.frames_folder, exist_ok=True)
        self.id_status_file = f"{output_path}_id_status.txt"

        # ROS发布者
        self.tracking_pub = rospy.Publisher('/tracker', TrackStatus, queue_size=10)

    def start_display_thread(self):
        """启动显示线程"""
        self.display_thread = threading.Thread(target=self.display_worker, daemon=True)
        self.display_thread.start()

    def display_worker(self):
        """显示线程工作函数"""
        while self.display_running:
            try:
                # 从队列获取帧（最多等待1秒）
                frame = self.display_queue.get(timeout=1)
                
                # 显示帧
                cv2.imshow('Tracking Preview', frame)
                
                # 处理键盘事件（按ESC退出）
                if cv2.waitKey(1) & 0xFF == 27:
                    rospy.signal_shutdown("User pressed ESC")
                    break
                
                # 控制显示帧率（约30FPS）
                self.display_queue.task_done()
            except queue.Empty:
                continue

    def process_frame(self, frame, dets, original_name):
        """处理并显示帧"""
        with self.lock:
            # 转换单通道图像
            if frame.ndim == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            current_ids = set()
            tracked_frame = frame.copy()  # 创建用于显示的副本

            if len(dets) > 0:
                cates = dets[:, 5]
                scores = dets[:, 4]

                online_targets = self.tracker.update_depth(dets, cates, scores)

                for t in online_targets:
                    x1, y1, x2, y2, tid, cate, st = map(int, t[:7])
                    current_ids.add(tid)

                    # 绘制跟踪框
                    color = self.colours[tid % 64]
                    cv2.rectangle(tracked_frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(
                        tracked_frame, f"ID: {tid}", (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2
                    )

                    # 更新ID状态
                    if tid not in self.id_status:
                        self.id_status[tid] = []
                    self.id_status[tid].append((self.frame_id, 1))

                    # 发布跟踪消息
                    track_msg = TrackStatus()
                    track_msg.track_id = tid
                    track_msg.status = 1
                    self.tracking_pub.publish(track_msg)

            # else:
                                # 获取当前所有跟踪器的状态（检测框）
            tracked_boxes = self.tracker.get_state()
            
            # 绘制持续跟踪的框（即使当前帧没有新检测）
            if tracked_boxes:  # 确保返回的boxes不为空
                for box in tracked_boxes:
                    if len(box) >= 4:  # 确保有完整的坐标[x1,y1,x2,y2]
                        x1, y1, x2, y2 ,tid= map(int, box[:5])
                        
                        # 使用特殊颜色（如黄色）标记持续跟踪的框
                        cv2.rectangle(tracked_frame, (x1, y1), (x2, y2), (0, 255, 255), 1)  # 黄色细框
                        cv2.putText(
                            tracked_frame, f"ID: {tid} ", (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1
                        )


            # 处理未出现的ID
            for tracked_id in self.id_status:
                if tracked_id not in current_ids:
                    self.id_status[tracked_id].append((self.frame_id, 0))
                    track_msg = TrackStatus()
                    track_msg.track_id = tracked_id
                    track_msg.status = 0
                    self.tracking_pub.publish(track_msg)

            # 保存帧
            frame_path = os.path.join(self.frames_folder, f"{original_name}.jpg")
            cv2.imwrite(frame_path, frame)
            # logger.info(f"Processed frame {self.frame_id}, saved to {frame_path}")
            self.frame_id += 1

            # 将带标注的帧加入显示队列
            try:
                self.display_queue.put_nowait(tracked_frame)
            except queue.Full:
                pass

    def save_id_status(self):
        with self.lock:
            with open(self.id_status_file, 'w') as f:
                for tid, status in self.id_status.items():
                    f.write(f"ID {tid}: {status}\n")
            # logger.info(f"ID status saved to {self.id_status_file}")

    def __del__(self):
        """析构函数确保资源释放"""
        self.display_running = False
        if self.display_thread and self.display_thread.is_alive():
            self.display_thread.join()
        cv2.destroyAllWindows()

def process_frame_thread(processor, detector, frame, image_name):
    try:
        dets = detector.detect(frame)
        dets = np.array(dets)
        processor.process_frame(frame, dets, image_name)
    except Exception as e:
        logger.error(f"Failed to process frame: {e}")

def image_callback(msg, args):
    processor, detector = args
    image = msg.image
    image_name = msg.image_name
    
    try:
        frame = detector.bridge.imgmsg_to_cv2(image, "mono8")
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    except Exception as e:
        logger.error(f"Failed to convert image: {e}")
        return

    threading.Thread(target=process_frame_thread, args=(processor, detector, frame, image_name)).start()

def main():
    rospy.init_node('image_detector_node', anonymous=True)

    output_path = "/home/<USER>/b6_"
    output_folder = "/home/<USER>/camera_images/output"

    tracker_params = {
        "det_thresh": 0.5,
        "iou_threshold": 0.3,
        "use_byte": False,
    }

    processor = ImageProcessor(output_path, tracker_params)
    detector = ROSImageDetector(output_folder)

    rospy.Subscriber("/camera/image_raw", ImageWithName, image_callback, (processor, detector), queue_size=30)

    try:
        rospy.spin()
    finally:
        processor.save_id_status()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main()