#!/usr/bin/env python

import os
import cv2
import numpy as np
from loguru import logger
from ocsort1 import <PERSON>CSort
from detect1 import ImageDetector
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import sys
import threading

sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from my_package.msg import Image<PERSON>ithName, TrackStatus

class ROSImageDetector:
    def __init__(self, output_folder, threshold=(40, 40, 40), area_threshold=10):
        self.detector = ImageDetector(output_folder, threshold, area_threshold)
        self.bridge = CvBridge()
        self.frame_id = 0
        self.lock = threading.Lock()

    def detect(self, frame):
        with self.lock:
            dets = self.detector.detect(frame)
            self.frame_id += 1
        return dets

class ImageProcessor:
    def __init__(self, output_path, tracker_params):
        self.output_path = output_path
        self.tracker_params = tracker_params

        self.tracker = OCSort(**tracker_params)
        self.colours = np.random.rand(64, 3) * 255
        self.id_status = {}
        self.frame_id = 0
        self.lock = threading.Lock()

        # 创建保存目录（修改为保存处理后的图像）
        self.frames_folder = f"{output_path}_processed_frames"
        os.makedirs(self.frames_folder, exist_ok=True)
        self.id_status_file = f"{output_path}_id_status.txt"

        # ROS发布者
        self.tracking_pub = rospy.Publisher('/tracker', TrackStatus, queue_size=10)

    def process_frame(self, frame, dets, original_name):
        """处理并保存带跟踪结果的帧"""
        with self.lock:
            # 转换单通道图像
            if frame.ndim == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            current_ids = set()
            tracked_frame = frame.copy()  # 创建用于保存的副本
            img_size = frame.shape[:2]

            if len(dets) > 0:


                online_targets = self.tracker.update_depth(dets[:, :6], img_size, img_size)

                for t in online_targets:
                    x1, y1, x2, y2,d,tid = map(int, t[:6])
                    current_ids.add(tid)

                    # 绘制跟踪框
                    color = self.colours[tid % 64]
                    cv2.rectangle(tracked_frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(
                        tracked_frame, f"ID: {tid}", (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2
                    )

                    # 更新ID状态
                    if tid not in self.id_status:
                        self.id_status[tid] = []
                    self.id_status[tid].append((self.frame_id, 1))

                    # 发布跟踪消息
                    track_msg = TrackStatus()
                    track_msg.track_id = tid
                    track_msg.status = 1
                    self.tracking_pub.publish(track_msg)
            # else:
            # 获取当前所有跟踪器的状态（检测框）
            tracked_boxes = self.tracker.get_state()
            
            # 绘制持续跟踪的框（即使当前帧没有新检测）
            if tracked_boxes:  # 确保返回的boxes不为空
                for box in tracked_boxes:
                    if len(box) >= 4:  # 确保有完整的坐标[x1,y1,x2,y2]
                        x1, y1, x2, y2 ,tid= map(int, box[:5])
                        print(tid)
                        # 更新ID状态
                        # 处理未出现的ID
                        if tid not in current_ids:
                            self.id_status[tid].append((self.frame_id, 0))
                            track_msg = TrackStatus()
                            track_msg.track_id = tid
                            track_msg.status = 0
                            self.tracking_pub.publish(track_msg)
                            print(tid)

                            # 使用特殊颜色（如黄色）标记持续跟踪的框
                            cv2.rectangle(tracked_frame, (x1, y1), (x2, y2), (0, 255, 255), 1)
                            cv2.putText(
                                tracked_frame, f"ID: {tid} ", (x1, y1 - 10),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1
                            )



            # 保存处理后的帧（包含跟踪信息）
            frame_path = os.path.join(self.frames_folder, f"{original_name}_processed.jpg")
            cv2.imwrite(frame_path, tracked_frame)
            logger.info(f"Saved processed frame: {frame_path}")
            
            self.frame_id += 1

    def save_id_status(self):
        with self.lock:
            with open(self.id_status_file, 'w') as f:
                for tid, status in self.id_status.items():
                    f.write(f"ID {tid}: {status}\n")
            logger.info(f"ID status saved to {self.id_status_file}")

def process_frame_thread(processor, detector, frame, image_name):
    # try:
    #     dets = detector.detect(frame)
    #     dets = np.array(dets)
    #     processor.process_frame(frame, dets, image_name)
    # except Exception as e:
    #     logger.error(f"Failed to process frame: {e}")
        dets = detector.detect(frame)
        dets = np.array(dets)
        processor.process_frame(frame, dets, image_name)


def image_callback(msg, args):
    processor, detector = args
    image = msg.image
    image_name = msg.image_name
    
    try:
        frame = detector.bridge.imgmsg_to_cv2(image, "mono8")
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    except Exception as e:
        logger.error(f"Failed to convert image: {e}")
        return

    threading.Thread(target=process_frame_thread, args=(processor, detector, frame, image_name)).start()

def main():
    rospy.init_node('image_detector_node', anonymous=True)

    output_path = "/home/<USER>/b7_"
    output_folder = "/home/<USER>/camera_images/output"

    tracker_params = {
        "det_thresh": 0.5,
        "iou_threshold": 0.3,
        "use_byte": False,
    }

    processor = ImageProcessor(output_path, tracker_params)
    detector = ROSImageDetector(output_folder)

    rospy.Subscriber("/camera/image_raw", ImageWithName, image_callback, (processor, detector), queue_size=30)

    try:
        rospy.spin()
    finally:
        processor.save_id_status()

if __name__ == "__main__":
    main()