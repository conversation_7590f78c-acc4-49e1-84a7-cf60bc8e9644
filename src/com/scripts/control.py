#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import rospy
from geometry_msgs.msg import Twist  # 导入Twist消息类型用于速度控制
from std_msgs.msg import Int32  # 导入Int32消息类型
import math

# 定义一个全局变量来存储速度信息
velocity_data = None

def decode_control_command(control_value):
    """
    解码控制命令，前5位表示方向，后2位表示速度。
    """
    # 将整数转换为8位二进制字符串
    binary_str = format(control_value, '07b')
    
    # 提取前5位（方向）和后2位（速度）
    direction_bits = binary_str[:5]
    speed_bits = binary_str[5:]
    
    # 将二进制字符串转换为整数
    direction = int(direction_bits, 2)
    speed = int(speed_bits, 2)
    
    return direction, speed

def callback(data):
    """
    回调函数，处理接收到的速度信息。
    """
    global velocity_data
    rospy.loginfo("Received VelocityXY:")
    
    # 假设data.data是接收到的整数
    control_value = data.data
    rospy.loginfo(f"Pontrol_value={control_value}")
    
    # 解码控制命令
    direction, speed = decode_control_command(control_value)
    
    # 打印解码后的方向和控制信息
    rospy.loginfo(f"Decoded Direction: {direction}, Speed: {speed}")
    
    # 发布控制信息
    publish_control_command(direction, speed)

def publish_control_command(direction, speed):
    """
    根据方向和控制信息发布控制指令。
    """
    # 创建Twist消息
    control_msg = Twist()
    
    # 将方向转换为角度（0-31对应0-360度）
    angle = direction * (360 / 32)  # 每个方向对应11.25度
    
    # 将角度转换为弧度
    angle_rad = math.radians(angle)
    
    # 计算x和y方向的速度分量
    control_msg.linear.x = speed * math.cos(angle_rad)*0.1
    control_msg.linear.y = speed * math.sin(angle_rad)*0.1
    
    # 发布控制消息到/robot/velcmd
    control_publisher.publish(control_msg)
    rospy.loginfo(f"Published control command: Direction={direction}, Speed={speed}, X={control_msg.linear.x}, Y={control_msg.linear.y}")

def velocity_xy_subscriber():
    """
    初始化ROS节点，设置发布者和订阅者。
    """
    global control_publisher  # 声明为全局变量以便在callback函数中使用
    
    # 初始化ROS节点
    rospy.init_node('control', anonymous=True)
    
    # 创建一个发布者，发布到/robot/velcmd
    control_publisher = rospy.Publisher('/robot/velcmd', Twist, queue_size=10)
    
    # 创建一个订阅者，订阅名为'velocity_xy'的主题，消息类型为Int32
    rospy.Subscriber('decoded_message', Int32, callback)
    
    # 保持节点运行，直到被中断
    rospy.spin()

if __name__ == '__main__':
    try:
        velocity_xy_subscriber()
    except rospy.ROSInterruptException:
        pass