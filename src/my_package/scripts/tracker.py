#!/usr/bin/env python

import os
import cv2
import numpy as np
from loguru import logger
from ocsort import OCSort
from detect import ImageDetector
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from datetime import datetime
import sys
import threading  # 导入 threading 模块

sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from my_package.msg import ImageWithName

class ROSImageDetector:
    def __init__(self, output_folder, threshold=(40, 40, 40), area_threshold=10):
        self.detector = ImageDetector(output_folder, threshold, area_threshold)
        self.bridge = CvBridge()
        self.frame_id = 0
        self.lock = threading.Lock()  # 添加线程锁

    def detect(self, frame):
        with self.lock:  # 加锁
            dets = self.detector.detect(frame)
            self.frame_id += 1
        return dets

class ImageProcessor:
    def __init__(self, output_path, tracker_params):
        self.output_path = output_path
        self.tracker_params = tracker_params

        self.tracker = OCSort(**tracker_params)
        self.colours = np.random.rand(64, 3) * 255  # Random colors for tracking IDs
        self.id_status = {}  # 用于记录ID是否出现过的字典
        self.frame_id = 0
        self.lock = threading.Lock()  # 添加线程锁

        # 创建保存帧的文件夹
        self.frames_folder = f"{output_path}_frames"
        os.makedirs(self.frames_folder, exist_ok=True)

        # 创建保存跟踪结果的 ID 状态文件
        self.id_status_file = f"{output_path}_id_status.txt"

    from datetime import datetime  # 导入 datetime 模块

    def process_frame(self, frame, dets, original_name):
        """
        处理每一帧图像，进行目标跟踪和绘制。
        """
        with self.lock:  # 加锁
            # 将单通道图像转换为三通道图像
            if frame.ndim == 2:  # 单通道图像
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
                                # 当前帧 ID 集合
            current_ids = set()

            if len(dets) > 0:
                cates = dets[:, 5]
                scores = dets[:, 4]  # 得分即为每个检测框的 confidence

                # 调用 update_public 方法
                online_targets = self.tracker.update_depth(dets, cates, scores)



                for t in online_targets:
                    x1, y1, x2, y2, tid, cate, st = map(int, t[:7])
                    current_ids.add(tid)

                    # 绘制跟踪框和ID
                    color = self.colours[tid % 64]
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(
                        frame, f"ID: {tid}", (x1, y1 - 10),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2
                    )

                    # 更新 ID 状态
                    if tid not in self.id_status:
                        self.id_status[tid] = []
                    self.id_status[tid].append((self.frame_id, 1))  # 当前帧该ID出现，记录为 (frame_id, 1)

            # 对于未在当前帧出现的 ID，记录为 (frame_id, 0)
            for tracked_id in self.id_status:
                if tracked_id not in current_ids:
                    self.id_status[tracked_id].append((self.frame_id, 0))

            # 使用原始图片的名字命名文件
            frame_path = os.path.join(self.frames_folder, f"{original_name}.jpg")

            # 保存当前帧
            cv2.imwrite(frame_path, frame)

            logger.info(f"Processed frame {self.frame_id}, saved to {frame_path}")
            self.frame_id += 1

    def save_id_status(self):
        """
        将 ID 状态保存到文件。
        """
        with self.lock:  # 加锁
            with open(self.id_status_file, 'w') as f:
                for tid, status in self.id_status.items():
                    f.write(f"ID {tid}: {status}\n")

            logger.info(f"ID status saved to {self.id_status_file}")

def process_frame_thread(processor, detector, frame, image_name):
    """
    多线程处理图像帧的函数。
    """
    try:
        # 使用 detect 函数进行检测
        dets = detector.detect(frame)
        dets = np.array(dets)

        # 处理当前帧
        processor.process_frame(frame, dets, image_name)  # 传递图像名字
    except Exception as e:
        logger.error(f"Failed to process frame: {e}")

def image_callback(msg, args):
    """
    ROS 图像回调函数。
    """
    processor, detector = args  # 解包额外参数
    
    # 获取图像和图像名字
    image = msg.image
    image_name = msg.image_name
    
    try:
        # 使用 detector 中的 bridge 转换图像
        frame = detector.bridge.imgmsg_to_cv2(image, "mono8")
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    except Exception as e:
        logger.error(f"Failed to convert image: {e}")
        return

    # 创建并启动一个新线程来处理图像
    threading.Thread(target=process_frame_thread, args=(processor, detector, frame, image_name)).start()

def main():
    # 初始化 ROS 节点
    rospy.init_node('image_detector_node', anonymous=True)

    # 设置路径和参数
    output_path = "/home/<USER>/b6_"
    output_folder = "/home/<USER>/camera_images/output"

    tracker_params = {
        "det_thresh": 0.5,
        "iou_threshold": 0.3,
        "use_byte": False,
    }

    # 创建处理器和检测器实例
    processor = ImageProcessor(output_path, tracker_params)
    detector = ROSImageDetector(output_folder)

    # 订阅图像话题，设置队列大小为 10
    rospy.Subscriber("/camera/image_raw", ImageWithName, image_callback, (processor, detector), queue_size=30)

    # 保持节点运行
    rospy.spin()

    # 保存 ID 状态
    processor.save_id_status()

if __name__ == "__main__":
    main()