#!/usr/bin/env python
import rospy
import cv2
import os
import mvsdk
import numpy as np
from cv_bridge import CvBridge
from sensor_msgs.msg import Image
from datetime import datetime
import platform
import time  # 导入time模块用于计算帧率

class Camera:
    def __init__(self, DevInfo):
        self.DevInfo = DevInfo
        self.hCamera = 0
        self.cap = None
        self.pFrameBuffer = 0

    def open(self):
        if self.hCamera > 0:
            return True

        try:
            self.hCamera = mvsdk.CameraInit(self.DevInfo, -1, -1)
        except mvsdk.CameraException as e:
            rospy.logerr("CameraInit Failed({}): {}".format(e.error_code, e.message))
            return False

        self.cap = mvsdk.CameraGetCapability(self.hCamera)
        monoCamera = (self.cap.sIspCapacity.bMonoSensor != 0)

        if monoCamera:
            mvsdk.CameraSetIspOutFormat(self.hCamera, mvsdk.CAMERA_MEDIA_TYPE_MONO8)
        else:
            mvsdk.CameraSetIspOutFormat(self.hCamera, mvsdk.CAMERA_MEDIA_TYPE_BGR8)

        FrameBufferSize = self.cap.sResolutionRange.iWidthMax * self.cap.sResolutionRange.iHeightMax * (1 if monoCamera else 3)
        self.pFrameBuffer = mvsdk.CameraAlignMalloc(FrameBufferSize, 16)

        mvsdk.CameraSetTriggerMode(self.hCamera, 0)
        mvsdk.CameraSetAeState(self.hCamera, 0)
        mvsdk.CameraSetExposureTime(self.hCamera, 2000)
        mvsdk.CameraPlay(self.hCamera)

        return True

    def close(self):
        if self.hCamera > 0:
            mvsdk.CameraUnInit(self.hCamera)
            self.hCamera = 0

        mvsdk.CameraAlignFree(self.pFrameBuffer)
        self.pFrameBuffer = 0

    def grab(self):
        try:
            pRawData, FrameHead = mvsdk.CameraGetImageBuffer(self.hCamera, 200)
            mvsdk.CameraImageProcess(self.hCamera, pRawData, self.pFrameBuffer, FrameHead)
            mvsdk.CameraReleaseImageBuffer(self.hCamera, pRawData)

            if platform.system() == "Windows":
                mvsdk.CameraFlipFrameBuffer(self.pFrameBuffer, FrameHead, 1)

            frame_data = (mvsdk.c_ubyte * FrameHead.uBytes).from_address(self.pFrameBuffer)
            frame = np.frombuffer(frame_data, dtype=np.uint8)
            frame = frame.reshape((FrameHead.iHeight, FrameHead.iWidth, 1 if FrameHead.uiMediaType == mvsdk.CAMERA_MEDIA_TYPE_MONO8 else 3))
            return frame
        except mvsdk.CameraException as e:
            if e.error_code != mvsdk.CAMERA_STATUS_TIME_OUT:
                rospy.logerr("CameraGetImageBuffer failed({}): {}".format(e.error_code, e.message))
            return None

class ImagePublisher:
    def __init__(self, publish_rate):
        rospy.init_node('image_publisher', anonymous=True)
        self.rate = rospy.Rate(publish_rate)
        self.image_pub = rospy.Publisher('/camera/image_raw', Image, queue_size=10)
        self.bridge = CvBridge()

        # 枚举相机
        DevList = mvsdk.CameraEnumerateDevice()
        if len(DevList) < 1:
            rospy.logerr("No camera was found!")
            rospy.signal_shutdown("No camera found")

        self.cam = Camera(DevList[0])
        if not self.cam.open():
            rospy.signal_shutdown("Failed to open camera")

        # 初始化帧率计算相关变量
        self.frame_count = 0
        self.start_time = time.time()

    def publish_images(self):
        while not rospy.is_shutdown():
            frame = self.cam.grab()
            if frame is not None:
                try:
                    ros_image = self.bridge.cv2_to_imgmsg(frame, "mono8")
                    self.image_pub.publish(ros_image)
                    self.frame_count += 1

                    # 计算帧率
                    elapsed_time = time.time() - self.start_time
                    fps = self.frame_count / elapsed_time
                    rospy.loginfo("Published image from camera. FPS: {:.2f}".format(fps))
                except Exception as e:
                    rospy.logerr("Failed to convert image to ROS message: {}".format(e))

            self.rate.sleep()

if __name__ == '__main__':
    try:
        publish_rate = rospy.get_param('~publish_rate', 20)  # 默认频率40Hz
        image_publisher = ImagePublisher(publish_rate)
        image_publisher.publish_images()
    except rospy.ROSInterruptException:
        pass