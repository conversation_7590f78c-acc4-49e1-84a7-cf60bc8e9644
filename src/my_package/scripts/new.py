import cv2
import numpy as np
import mvsdk
import platform
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import os
from datetime import datetime
import threading

class Camera(object):
    def __init__(self, DevInfo):
        super(Camera, self).__init__()
        self.DevInfo = DevInfo
        self.hCamera = 0
        self.cap = None
        self.pFrameBuffer = 0

    def open(self):
        if self.hCamera > 0:
            return True

        # 打开相机
        hCamera = 0
        try:
            hCamera = mvsdk.CameraInit(self.DevInfo, -1, -1)
        except mvsdk.CameraException as e:
            print("CameraInit Failed({}): {}".format(e.error_code, e.message))
            return False

        # 获取相机特性描述
        cap = mvsdk.CameraGetCapability(hCamera)

        # 判断是黑白相机还是彩色相机
        monoCamera = (cap.sIspCapacity.bMonoSensor != 0)

        # 黑白相机让ISP直接输出MONO数据，而不是扩展成R=G=B的24位灰度
        if monoCamera:
            mvsdk.CameraSetIspOutFormat(hCamera, mvsdk.CAMERA_MEDIA_TYPE_MONO8)
        else:
            mvsdk.CameraSetIspOutFormat(hCamera, mvsdk.CAMERA_MEDIA_TYPE_BGR8)

        # 计算RGB buffer所需的大小，这里直接按照相机的最大分辨率来分配
        FrameBufferSize = cap.sResolutionRange.iWidthMax * cap.sResolutionRange.iHeightMax * (1 if monoCamera else 3)

        # 分配RGB buffer，用来存放ISP输出的图像
        pFrameBuffer = mvsdk.CameraAlignMalloc(FrameBufferSize, 16)

        # 相机模式切换成连续采集
        mvsdk.CameraSetTriggerMode(hCamera, 0)
        mvsdk.CameraSetFrameSpeed(hCamera, 2)

        # 手动曝光，曝光时间100微秒
        mvsdk.CameraSetAeState(hCamera, 0)
        mvsdk.CameraSetExposureTime(hCamera, 5000)

        # 让SDK内部取图线程开始工作
        mvsdk.CameraPlay(hCamera)

        self.hCamera = hCamera
        self.pFrameBuffer = pFrameBuffer
        self.cap = cap
        return True

    def close(self):
        if self.hCamera > 0:
            mvsdk.CameraUnInit(self.hCamera)
            self.hCamera = 0

        mvsdk.CameraAlignFree(self.pFrameBuffer)
        self.pFrameBuffer = 0

    def grab(self):
        # 从相机取一帧图片
        hCamera = self.hCamera
        pFrameBuffer = self.pFrameBuffer
        try:
            pRawData, FrameHead = mvsdk.CameraGetImageBuffer(hCamera, 200)
            mvsdk.CameraImageProcess(hCamera, pRawData, pFrameBuffer, FrameHead)
            mvsdk.CameraReleaseImageBuffer(hCamera, pRawData)

            # windows下取到的图像数据是上下颠倒的，以BMP格式存放。转换成opencv则需要上下翻转成正的
            # linux下直接输出正的，不需要上下翻转
            if platform.system() == "Windows":
                mvsdk.CameraFlipFrameBuffer(pFrameBuffer, FrameHead, 1)

            # 此时图片已经存储在pFrameBuffer中，对于彩色相机pFrameBuffer=RGB数据，黑白相机pFrameBuffer=8位灰度数据
            # 把pFrameBuffer转换成opencv的图像格式以进行后续算法处理
            frame_data = (mvsdk.c_ubyte * FrameHead.uBytes).from_address(pFrameBuffer)
            frame = np.frombuffer(frame_data, dtype=np.uint8)
            frame = frame.reshape((FrameHead.iHeight, FrameHead.iWidth, 1 if FrameHead.uiMediaType == mvsdk.CAMERA_MEDIA_TYPE_MONO8 else 3))
            return frame
        except mvsdk.CameraException as e:
            if e.error_code != mvsdk.CAMERA_STATUS_TIME_OUT:
                print("CameraGetImageBuffer failed({}): {}".format(e.error_code, e.message))
            return None


def save_image(frame, save_dir, time_str):
    """保存图像到磁盘"""
    file_name = f"image_{time_str}.jpg"
    file_path = os.path.join(save_dir, file_name)
    cv2.imwrite(file_path, frame)
    # rospy.loginfo(f"Saved image: {file_path}")


def main_loop():
    # 初始化ROS节点
    rospy.init_node('camera_node', anonymous=True)
    bridge = CvBridge()

    # 设置保存图像的目录
    save_dir = os.path.join(os.path.expanduser("~"), "camera_images40")
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        rospy.loginfo(f"Created directory: {save_dir}")

    # 枚举相机
    DevList = mvsdk.CameraEnumerateDevice()
    nDev = len(DevList)
    if nDev < 1:
        print("No camera was found!")
        return

    for i, DevInfo in enumerate(DevList):
        print("{}: {} {}".format(i, DevInfo.GetFriendlyName(), DevInfo.GetPortType()))

    cams = []
    for i in map(lambda x: int(x), input("Select cameras: ").split()):
        cam = Camera(DevList[i])
        if cam.open():
            cams.append(cam)

    # 创建图像发布者
    image_pubs = [rospy.Publisher('camera/image_raw_{}'.format(i), Image, queue_size=10) for i in range(len(cams))]

    # 设置目标帧率（40Hz）
    target_fps = 50
    rate = rospy.Rate(target_fps)  # 使用rospy.Rate控制频率

    # 初始化帧率统计
    frame_count = 0
    start_time = rospy.Time.now()
    pre=0
    pre=mvsdk.CameraGetFrameStatistic(cam.hCamera).iCapture

    def calculate_fps(event):
        nonlocal pre,start_time,frame_count

        print(mvsdk.CameraGetFrameStatistic(cam.hCamera).iCapture)
        current=mvsdk.CameraGetFrameStatistic(cam.hCamera).iCapture
        elapsed_time = (rospy.Time.now() - start_time).to_sec()
        fps=frame_count/elapsed_time
        # fps=current-pre
        rospy.loginfo(f"Current FPS: {fps:.2f}")
        start_time=rospy.Time.now()
        frame_count=0
        pre=current

    # 设置1秒定时器
    rospy.Timer(rospy.Duration(1), calculate_fps)

    while not rospy.is_shutdown():
        for i, cam in enumerate(cams):
            frame = cam.grab()
            if frame is not None:
                # 将OpenCV图像转换为ROS图像消息
                ros_image = bridge.cv2_to_imgmsg(frame, "mono8")
                # 发布图像
                image_pubs[i].publish(ros_image)

                # 获取当前时间（精确到毫秒）
                current_time = datetime.now()
                time_str = current_time.strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 去掉最后3位微秒，保留毫秒

                # 使用多线程保存图像，避免阻塞主循环
                threading.Thread(target=save_image, args=(frame, save_dir, time_str)).start()

                # 增加帧计数
                frame_count += 1

        rate.sleep()  # 使用rospy.Rate控制循环频率

    for cam in cams:
        cam.close()


if __name__ == '__main__':
    try:
        main_loop()
    except rospy.ROSInterruptException:
        pass