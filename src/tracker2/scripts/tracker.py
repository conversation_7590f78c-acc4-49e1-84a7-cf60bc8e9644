#!/usr/bin/env python

import os
import cv2
import numpy as np
from loguru import logger
from ocsort import OCSort
from detect import ImageDetector
import rospy
from sensor_msgs.msg import Image
from cv_bridge import CvBridge
from datetime import datetime

class ROSImageDetector:
    def __init__(self, output_folder, threshold=(40, 40, 40), area_threshold=10):
        self.detector = ImageDetector(output_folder, threshold, area_threshold)
        self.bridge = CvBridge()
        self.frame_id = 0

    def detect(self, frame):
        dets = self.detector.detect(frame)
        self.frame_id += 1
        return dets

class ImageProcessor:
    def __init__(self, output_path, tracker_params):
        self.output_path = output_path
        self.tracker_params = tracker_params

        self.tracker = OCSort(**tracker_params)
        self.colours = np.random.rand(64, 3) * 255  # Random colors for tracking IDs
        self.id_status = {}  # 用于记录ID是否出现过的字典
        self.frame_id = 0

        # 创建保存帧的文件夹
        self.frames_folder = f"{output_path}_frames"
        os.makedirs(self.frames_folder, exist_ok=True)

        # 创建保存跟踪结果的 ID 状态文件
        self.id_status_file = f"{output_path}_id_status.txt"

    from datetime import datetime  # 导入 datetime 模块

    def process_frame(self, frame, dets):
        """
        处理每一帧图像，进行目标跟踪和绘制。
        """
        # 将单通道图像转换为三通道图像
        if frame.ndim == 2:  # 单通道图像
            frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

        if len(dets) > 0:
            cates = dets[:, 5]
            scores = dets[:, 4]  # 得分即为每个检测框的 confidence

            # 调用 update_public 方法
            online_targets = self.tracker.update_depth(dets, cates, scores)

            # 当前帧 ID 集合
            current_ids = set()

            for t in online_targets:
                x1, y1, x2, y2, tid, cate, st = map(int, t[:7])
                current_ids.add(tid)

                # 绘制跟踪框和ID
                color = self.colours[tid % 64]
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                cv2.putText(
                    frame, f"ID: {tid}", (x1, y1 - 10),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2
                )

                # 更新 ID 状态
                if tid not in self.id_status:
                    self.id_status[tid] = []
                self.id_status[tid].append((self.frame_id, 1))  # 当前帧该ID出现，记录为 (frame_id, 1)

            # 对于未在当前帧出现的 ID，记录为 (frame_id, 0)
            for tracked_id in self.id_status:
                if tracked_id not in current_ids:
                    self.id_status[tracked_id].append((self.frame_id, 0))

        # 使用当前时间（精确到毫秒）命名文件
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S_%f")  # 格式：年月日_时分秒_毫秒
        frame_path = os.path.join(self.frames_folder, f"frame_{current_time}.jpg")

        # 保存当前帧
        cv2.imwrite(frame_path, frame)

        logger.info(f"Processed frame {self.frame_id}, saved to {frame_path}")
        self.frame_id += 1

    def save_id_status(self):
        """
        将 ID 状态保存到文件。
        """
        with open(self.id_status_file, 'w') as f:
            for tid, status in self.id_status.items():
                f.write(f"ID {tid}: {status}\n")

        logger.info(f"ID status saved to {self.id_status_file}")

def image_callback(msg, args):
    """
    ROS 图像回调函数。
    """
    processor, detector = args  # 解包额外参数
    try:
        # 使用 detector 中的 bridge 转换图像
        frame = detector.bridge.imgmsg_to_cv2(msg, "mono8")
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)
    except Exception as e:
        logger.error(f"Failed to convert image: {e}")
        return

    try:
        # 使用 detect 函数进行检测
        dets = detector.detect(frame)
        dets = np.array(dets)

        # 处理当前帧
        processor.process_frame(frame, dets)
    except Exception as e:
        logger.error(f"Failed to process frame: {e}")

def main():
    # 初始化 ROS 节点
    rospy.init_node('image_detector_node', anonymous=True)

    # 设置路径和参数
    output_path = "/home/<USER>/b6_"
    output_folder = "/home/<USER>/camera_images/output"

    tracker_params = {
        "det_thresh": 0.5,
        "iou_threshold": 0.3,
        "use_byte": False,
    }

    # 创建处理器和检测器实例
    processor = ImageProcessor(output_path, tracker_params)
    detector = ROSImageDetector(output_folder)

    # 订阅图像话题
    rospy.Subscriber("/camera/image_raw", Image, image_callback, (processor, detector))

    # 保持节点运行
    rospy.spin()

    # 保存 ID 状态
    processor.save_id_status()

if __name__ == "__main__":
    main()