#!/usr/bin/env python3
import rospy
import sys
sys.path.append('/home/<USER>/catkin_ws/devel/lib/python3/dist-packages')
from com.msg import ControlCommand

def publish_led_command():
    rospy.init_node('com_publisher', anonymous=True)
    pub = rospy.Publisher('control_command', ControlCommand, queue_size=10)

    while not rospy.is_shutdown():
        try:
            direction = int(input("请输入方向 (0-31): "))
            speed = int(input("请输入速度 (0-3): "))

            if not (0 <= direction <= 31):
                raise ValueError("方向必须在0到31之间。")
            if not (0 <= speed <= 3):
                raise ValueError("速度必须在0到3之间。")

            command_msg = ControlCommand()
            command_msg.direction = direction
            command_msg.speed = speed
            pub.publish(command_msg)
            rospy.loginfo(f"Published command: direction={direction}, speed={speed}")

        except ValueError as e:
            rospy.logerr(f"输入错误: {e}")
        except rospy.ROSInterruptException:
            rospy.loginfo("节点已关闭。")
            break

if __name__ == '__main__':
    try:
        publish_led_command()
    except rospy.ROSInterruptException:
        pass