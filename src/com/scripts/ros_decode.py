#!/usr/bin/env python3
import rospy
from std_msgs.msg import Int32 ,String # 修改为发布 Int32 类型
import threading

class DecoderNode:
    def __init__(self, base_frequency=1, signal_length=11, repeat_count=3):
        # 初始化 ROS 节点
        rospy.init_node('signal_decoder', anonymous=True)
        
        # 订阅 tracker 话题
        self.tracker_sub = rospy.Subscriber('/tracker', String, self.tracker_callback)
        
        # 发布解码后的消息话题，修改为 Int32 类型
        self.decoded_pub = rospy.Publisher('/decoded_message', Int32, queue_size=10)
        
        # 示例输入参数
        self.base_frequency = base_frequency
        self.signal_length = signal_length
        self.repeat_count = repeat_count
        self.packet = (self.base_frequency * self.signal_length * self.repeat_count) + 5
        self.pattern = [1, 1, 0]  # 需要检测的模式

        # 初始化状态
        self.tracking_states = {}  # 用于存储每个 tracking_id 的状态
        self.lock = threading.Lock()  # 用于线程安全的锁

    def tracker_callback(self, data):
        """
        订阅 tracker 话题的回调函数。
        消息格式为：ID: {tracking_id}, Message: {received_message}
        """
        # 解析接收到的消息
        message = data.data  # 获取消息内容
        try:
            # 提取 ID 和 Message
            parts = message.split(", Message: ")
            if len(parts) == 2:
                tracking_id = parts[0].replace("ID: ", "").strip()  # 提取 ID
                received_bit = parts[1].strip()  # 提取单个数据位
                rospy.loginfo(f"Received bit - ID: {tracking_id}, Bit: {received_bit}")
                
                # 将接收到的数据位转换为整数
                bit = int(received_bit)
                
                # 初始化或获取当前 tracking_id 的状态
                with self.lock:
                    if tracking_id not in self.tracking_states:
                        self.tracking_states[tracking_id] = {
                            'buffer': [],  # 用于存储接收到的二进制数据流
                            'is_collecting': False,  # 是否正在收集数据
                        }
                    state = self.tracking_states[tracking_id]
                    
                    # 将新数据位添加到缓冲区
                    state['buffer'].append(bit)
                    
                    # 如果缓冲区长度足够，检查是否匹配模式
                    if len(state['buffer']) >= len(self.pattern) and not state['is_collecting']:
                        # 使用滑动窗口检查是否匹配模式
                        window = state['buffer'][-len(self.pattern):]  # 取最后 pattern_len 个数据位
                        if window == self.pattern:
                            # 检测到完整模式 110，开始收集数据
                            state['is_collecting'] = True
                            
                            # 保留最后一位数据
                            last_bit = state['buffer'][-1]  # 获取最后一位数据
                            state['buffer'] = [last_bit]  # 清空缓冲区，但保留最后一位数据
                            
                            rospy.loginfo(f"ID: {tracking_id} - Pattern 110 detected. Start collecting data.")
                    
                    # 如果正在收集数据
                    if state['is_collecting']:
                        # 检查是否收集到足够的数据
                        if len(state['buffer']) >= self.packet:
                            # 提取信号并解码
                            signal = state['buffer'][:self.packet]
                            rospy.loginfo(f"ID: {tracking_id} - Decoding signal: {signal}")
                            
                            # 重置状态
                            state['is_collecting'] = False
                            state['buffer'] = []
                            
                            # 启动新线程进行解码
                            threading.Thread(
                                target=self.decode_and_publish,
                                args=(tracking_id, signal),
                                daemon=True
                            ).start()
            else:
                rospy.logwarn(f"Invalid message format: {message}")
        except Exception as e:
            rospy.logerr(f"Error processing message: {e}")

    def decode_and_publish(self, tracking_id, signal):
        """
        解码信号并发布结果。
        """
        decoded_messages = self.decode_signal(signal)
        if decoded_messages:
            # 检查解码后的消息列表
            if len(decoded_messages) == 3:
                # 检查三个值是否都不同
                if len(set(decoded_messages)) == 3:
                    rospy.loginfo("All three values are different. Decoding failed.")
                else:
                    # 使用投票机制决定发布的值
                    from collections import Counter
                    counter = Counter(decoded_messages)
                    most_common_value = counter.most_common(1)[0][0]
                    
                    # 发布解码后的消息
                    with self.lock:
                        self.decoded_pub.publish(most_common_value)
                        rospy.loginfo(f"ID: {tracking_id} - Published decoded message: {most_common_value}")
            else:
                rospy.logwarn(f"Decoded messages count is not 3: {decoded_messages}")
        else:
            rospy.logwarn("No valid decoded messages.")

    def decode_signal(self, signal):
        """
        解码信号。
        """
        decoded_messages = []
        start_index = 0
        end_index=9
        
        while start_index + self.signal_length <= len(signal):
            message_packet = signal[start_index:start_index + self.signal_length]
            end, flag = self.decode_message_packet(message_packet,end_index)
            if flag:
                decoded_messages.append(flag)
                start_index += end+2
                end_index=end
            elif end:
                start_index += end+2
                end_index=end
            else:
                start_index += 1  # 滑动窗口
                end_index=start_index+9
        
        return decoded_messages if decoded_messages else None

    def decode_message_packet(self, message_packet,end_index):
        """
        解码消息包。
        """
        try:
            # 1. 查找结束位
            end_index, flag2 = self.find_end_index_in_packet(message_packet,end_index,end_index+3)
            rospy.loginfo(f"End index: {end_index}, Flag2: {flag2}")

            # 2. 进行奇校验
            is_valid = self.parity_check(message_packet)
            rospy.loginfo(f"Parity check result: {is_valid}")

            if is_valid:
                # 3. 提取有效消息
                valid_message = message_packet[1:-3]  # 提取有效消息（去掉起始位和校验位）
                rospy.loginfo(f"Valid message: {valid_message}")

                # 4. 解码消息
                decoded_value = self.decode_binary_message(valid_message)
                return end_index, decoded_value
            elif flag2 == 0:
                rospy.logwarn("Parity check failed. Discarding message.")
                return end_index, None
            else:
                rospy.logwarn("End pattern not found. Discarding message.")
                return None, None
        except Exception as e:
            rospy.logerr(f"Error decoding message packet: {e}")
            return None

    def find_end_index_in_packet(self, message_packet, search_window_start=9, search_window_end=12, pattern=[1, 1, 0]):
        """
        在消息包中查找结束位。如果没有找到结束位，返回默认结束位。
        """
        flag2 = 1
        search_start = search_window_start
        search_end = min(search_window_end, len(message_packet))  # 限制最大搜索范围
        end_index = search_window_start   # 默认结束位为 Pattern indices + 8（相对于消息包）

        # 在消息包范围内查找结束位
        for i in range(search_start, search_end - 2):
            if message_packet[i:i + 3] == pattern:
                end_index = i + 2  # 结束位设置为 110 的结束位置
                flag2 = 0
                break

        return end_index, flag2

    def parity_check(self, message):
        """
        在起始位与结束位之间进行奇校验。
        """
        # 起始位后第7位为数据位
        data_start = 1
        # 校验位为结束位前一位
        parity_index = 8
        
        # 提取数据位
        data_bits = message[data_start:parity_index]
        if len(data_bits) == 0:  # 确保数据位存在
            raise ValueError("数据位长度为 0，无法进行校验。")
        
        # 计算数据位的奇校验结果
        parity_bit = message[parity_index]
        parity_calculated = 1 if data_bits.count(1) % 2 == 0 else 0  # 奇校验：求和后取模 2
        
        # 校验是否通过
        return parity_calculated == parity_bit

    def decode_binary_message(self, binary_message):
        """
        将二进制消息解码为整数值。
        """
        try:
            binary_str = ''.join(map(str, binary_message))  # 将二进制列表转换为字符串
            decoded_value = int(binary_str, 2)  # 将二进制字符串转换为整数
            rospy.loginfo(f"Pdecoded_value={decoded_value}")

            return decoded_value
        except Exception as e:
            rospy.logerr(f"Error decoding binary message: {e}")
            return None


if __name__ == '__main__':
    try:
        decoder_node = DecoderNode()
        rospy.spin()
    except rospy.ROSInterruptException:
        pass