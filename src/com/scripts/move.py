#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import rospy
from geometry_msgs.msg import Twist
import math

def get_user_input(prompt, min_val, max_val):
    """
    获取并验证用户输入，确保输入在指定范围内的整数
    """
    while True:
        try:
            value = int(input(prompt))
            if min_val <= value <= max_val:
                return value
            print(f"错误：输入值需在{min_val}-{max_val}之间")
        except ValueError:
            print("错误：请输入有效整数")

def control_robot():
    # 初始化ROS节点
    rospy.init_node('robot_control', anonymous=True)
    
    # 创建Publisher，发布到/robot/velcmd
    pub = rospy.Publisher('/robot/velcmd', Twist, queue_size=10)
    
    # 获取用户输入
    print("\n=== 小车运动控制 ===")
    direction = get_user_input("请输入方向 (0-31, 对应0-360°): ", 0, 31)
    speed = get_user_input("请输入速度等级 (0-3): ", 0, 3)
    
    # 计算基础运动参数
    angle = direction * (360.0 / 32)         # 转换为角度
    angle_rad = math.radians(angle)          # 转为弧度
    speed_scaled = speed * 0.1               # 速度缩放
    
    # 计算基础速度分量
    base_speed_x = speed_scaled * math.cos(angle_rad)
    base_speed_y = speed_scaled * math.sin(angle_rad)
    
    # 创建Twist消息对象
    twist_msg = Twist()
    
    # 显示控制信息
    print("\n控制参数已设置：")
    print(f"方向：{direction} → {angle:.2f}°")
    print(f"速度等级：{speed} → 实际速度：{speed_scaled} m/s")
    print(f"X轴分量：{base_speed_x:.2f} m/s")
    print(f"Y轴分量：{base_speed_y:.2f} m/s")
    print("机器人将每10秒自动反向移动... (按 Ctrl+C 停止)\n")
    
    # 初始化运动控制参数
    current_speed = 1  # 速度方向系数（1正向/-1反向）
    start_time = rospy.Time.now()
    
    # 持续发布控制指令
    rate = rospy.Rate(3)  # 10Hz控制频率
    while not rospy.is_shutdown():
        # 检查时间并切换方向
        current_time = rospy.Time.now()
        elapsed = (current_time - start_time).to_sec()
        
        if elapsed >= 10:
            current_speed *= -1
            start_time = current_time  # 重置计时器
            print(f"方向已切换至{'反向' if current_speed < 0 else '正向'}...")
        
        # 设置当前速度分量
        twist_msg.linear.x = current_speed * base_speed_x
        twist_msg.linear.y = current_speed * base_speed_y
        
        # 发布控制指令
        pub.publish(twist_msg)
        rate.sleep()

if __name__ == '__main__':
    try:
        control_robot()
    except rospy.ROSInterruptException:
        pass