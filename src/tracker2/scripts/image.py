#!/usr/bin/env python
import rospy
import cv2
import os
from cv_bridge import CvBridge
from sensor_msgs.msg import Image

class ImagePublisher:
    def __init__(self, folder_path, publish_rate):
        """
        初始化图片发布器
        :param folder_path: 包含图片的文件夹路径
        :param publish_rate: 发布频率（Hz）
        """
        # 初始化ROS节点
        rospy.init_node('image_publisher', anonymous=True)
        
        # 设置发布频率
        self.rate = rospy.Rate(publish_rate)
        
        # 初始化图片发布话题
        self.image_pub = rospy.Publisher('/camera/image_raw', Image, queue_size=10)
        
        # 初始化cv_bridge
        self.bridge = CvBridge()
        
        # 获取文件夹中的所有图片文件
        self.image_files = sorted([os.path.join(folder_path, f) for f in os.listdir(folder_path)
                                   if f.endswith(('.png', '.jpg', '.jpeg', '.bmp'))])
        
        # 检查是否有图片文件
        if not self.image_files:
            rospy.logerr("No image files found in the folder: %s", folder_path)
            rospy.signal_shutdown("No image files found")
        
        rospy.loginfo("Found %d images in the folder: %s", len(self.image_files), folder_path)
        
    def publish_images(self):
        """
        按照指定频率发布图片
        """
        while not rospy.is_shutdown():
            for image_file in self.image_files:
                # 读取图片
                image = cv2.imread(image_file)
                if image is None:
                    rospy.logwarn("Failed to read image file: %s", image_file)
                    continue
                
                # 转换为ROS消息格式
                try:
                    ros_image = self.bridge.cv2_to_imgmsg(image, encoding="bgr8")
                except Exception as e:
                    rospy.logerr("Failed to convert image to ROS message: %s", e)
                    continue
                
                # 发布图片
                self.image_pub.publish(ros_image)
                rospy.loginfo("Published image: %s", image_file)
                
                # 等待指定的时间间隔
                self.rate.sleep()

if __name__ == '__main__':
    try:
        # 设置图片文件夹路径和发布频率
        folder_path = rospy.get_param('~folder_path', '/home/<USER>/camera')  # 默认路径
        publish_rate = rospy.get_param('~publish_rate', 40)  # 默认频率1Hz
        
        # 创建图片发布器
        image_publisher = ImagePublisher(folder_path, publish_rate)
        
        # 开始发布图片
        image_publisher.publish_images()
    except rospy.ROSInterruptException:
        pass